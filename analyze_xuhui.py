#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
该脚本读取地震动加速度时程文件, 自动执行时程分析并保存桥梁响应数据
输入文件为numpy数组, shape为(n, T), n为地震动数量, T为时间维度
输出为npz文件, 包含桥墩位移、支座位移和支座剪力数据
"""

import os
import numpy as np
from typing import Dict, List, Tuple, Optional

# 导入桥梁模型和分析器
from params import BridgeParams
from core.simply_supported_beam_model import SimplySupportedBeamModel
from analysis.analyzer import BridgeAnalyzer
from analysis.recorder.pier import (
    find_piers_closest_to_y0,
    read_pier_displacement_data
)
from analysis.recorder.bearing import (
    find_bearings_closest_to_y0,
    read_bearing_force_data,
    read_bearing_deformation_data
)

def get_representative_piers(model) -> Dict[float, Tuple[int, int]]:
    """
    根据实际桥墩配置自动选择记录的桥墩数量和编号
    每个盖梁下只选择最靠近y=0的单个桥墩

    参数:
        model: 桥梁模型对象

    返回:
        Dict[float, Tuple[int, int]]: x坐标到桥墩键值(longitudinal_idx, transverse_idx)的映射
    """
    return find_piers_closest_to_y0(model)


def get_representative_bearings(model) -> List[int]:
    """
    获取代表性支座索引
    每个桥台/盖梁上选择靠近y=0的1或2个代表性支座

    参数:
        model: 桥梁模型对象

    返回:
        List[int]: 代表性支座索引列表
    """
    return find_bearings_closest_to_y0(model)


def run_analysis(
    params: BridgeParams,
    ground_motion, dt=0.01, direction=1, pga=0.15) -> Tuple[Dict, Dict, Dict]:
    """
    执行单个地震动的分析

    参数:
        params: 桥梁参数对象
        ground_motion: 地震记录数据
        dt: 时间步长
        direction: 地震方向 (1=纵向, 2=横向, 3=竖向)
        pga: 峰值地面加速度

    返回:
        pier_data: 桥墩位移数据字典
        bearing_data: 支座响应数据字典
        analysis_stats: 分析统计信息
    """
    # 创建桥梁模型
    model = SimplySupportedBeamModel(params)

    # 创建分析器
    analyzer = BridgeAnalyzer(model)
    analyzer.direction = direction

    # 运行分析
    analyzer.modal()
    analysis_stats = analyzer.dynamic(h=ground_motion, pga=pga, dt=dt)

    # 初始化返回数据
    pier_data = {}
    bearing_data = {}

    # 检查记录器信息
    if not analyzer.recorder_info:
        print("警告: 没有有效的记录器信息")
        return pier_data, bearing_data, analysis_stats

    # 读取桥墩位移数据
    if 'piers' in analyzer.recorder_info:
        pier_data = read_pier_data(analyzer.recorder_info['piers'])

    # 读取支座响应数据
    if 'bearings' in analyzer.recorder_info:
        bearing_data = read_bearing_data(analyzer.recorder_info['bearings'])

    return pier_data, bearing_data, analysis_stats


def read_pier_data(pier_recorder_info: Dict) -> Dict:
    """
    读取所有桥墩的位移数据

    参数:
        pier_recorder_info: 桥墩记录器信息字典

    返回:
        Dict: 桥墩位移数据字典
    """
    pier_data = {}

    for _, pier_info in pier_recorder_info.items():
        pier_key = pier_info['pier_key']
        disp_data = read_pier_displacement_data(pier_info)

        if disp_data is not None:
            pier_data[pier_key] = disp_data

    return pier_data


def read_bearing_data(bearing_recorder_info: Dict) -> Dict:
    """
    读取所有支座的响应数据（位移和剪力）

    参数:
        bearing_recorder_info: 支座记录器信息字典

    返回:
        Dict: 支座响应数据字典
    """
    bearing_data = {}

    for _, bearing_info in bearing_recorder_info.items():
        bearing_idx = bearing_info['bearing_idx']

        # 读取支座力数据
        force_data = read_bearing_force_data(bearing_info['force_file'])

        # 读取支座变形数据
        deform_data = read_bearing_deformation_data(bearing_info['deform_file'])

        if force_data and deform_data:
            bearing_data[bearing_idx] = {
                'x_coord': bearing_info['x_coord'],
                'y_coord': bearing_info['y_coord'],
                'force_data': force_data,
                'deform_data': deform_data
            }

    return bearing_data


def process_analysis_results(pier_data: Dict, bearing_data: Dict, n_steps: int) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """
    处理分析结果，提取代表性数据并格式化为数组

    参数:
        pier_data: 桥墩位移数据
        bearing_data: 支座响应数据
        n_steps: 时间步数

    返回:
        pier_displacements: 桥墩位移数组
        bearing_displacements: 支座位移数组
        bearing_forces: 支座剪力数组
    """
    # 获取代表性桥墩数据
    pier_keys = sorted(pier_data.keys()) if pier_data else []
    n_piers = len(pier_keys)

    # 处理单跨结构（无桥墩）的情况
    if n_piers == 0:
        pier_displacements = np.zeros((0, n_steps))  # 空数组，维度正确
    else:
        pier_displacements = np.zeros((n_piers, n_steps))

        for i, pier_key in enumerate(pier_keys):
            if pier_key in pier_data:
                disp_x = pier_data[pier_key]['disp_x']
                # 确保长度匹配
                if len(disp_x) > n_steps:
                    disp_x = disp_x[:n_steps]
                elif len(disp_x) < n_steps:
                    temp = np.zeros(n_steps)
                    temp[:len(disp_x)] = disp_x
                    disp_x = temp
                pier_displacements[i, :] = disp_x

    # 获取代表性支座数据
    bearing_indices = sorted(bearing_data.keys()) if bearing_data else []
    n_bearings = len(bearing_indices)

    if n_bearings == 0:
        # 如果没有支座数据，返回空数组
        bearing_displacements = np.zeros((0, n_steps))
        bearing_forces = np.zeros((0, n_steps))
        print("警告: 没有支座数据")
    else:
        bearing_displacements = np.zeros((n_bearings, n_steps))
        bearing_forces = np.zeros((n_bearings, n_steps))

        for i, bearing_idx in enumerate(bearing_indices):
            if bearing_idx in bearing_data:
                # 支座相对位移（X方向）
                deform_x = bearing_data[bearing_idx]['deform_data']['deform_x']
                # 支座剪力（X方向）
                force_x = bearing_data[bearing_idx]['force_data']['force_x']

                # 确保长度匹配
                if len(deform_x) > n_steps:
                    deform_x = deform_x[:n_steps]
                    force_x = force_x[:n_steps]
                elif len(deform_x) < n_steps:
                    temp_deform = np.zeros(n_steps)
                    temp_force = np.zeros(n_steps)
                    temp_deform[:len(deform_x)] = deform_x
                    temp_force[:len(force_x)] = force_x
                    deform_x = temp_deform
                    force_x = temp_force

                bearing_displacements[i, :] = deform_x
                bearing_forces[i, :] = force_x

    return pier_displacements, bearing_displacements, bearing_forces


def main():
    """
    主函数 - 改进版

    执行地震动时程分析，自动选择代表性桥墩和支座进行记录，
    并将结果保存为npz格式文件。
    """
    # 分析参数设置
    dt = 0.02      # 时间步长 (s)
    pga = 0.15     # 峰值地面加速度 (g)

    # 设置输入输出路径
    input_file = "gm/acc_artificial_tra.npy"
    config_file = "E:/Codes/opensees/徐汇区桥梁群/data/configs/bridge-4-龙川北路桥.json"
    output_file = input_file.replace('acc', 'response').replace('.npy', '.npz')

    # 验证输入参数
    if not validate_input_parameters(input_file, config_file, dt, pga):
        return

    # 读取模型参数
    try:
        params = BridgeParams(config_file)
        print(f"成功读取桥梁配置: {config_file}")
    except Exception as e:
        print(f"读取桥梁配置文件时出错: {e}")
        return

    # 读取地震动数据
    print(f"读取地震动数据: {input_file}")
    try:
        ground_motions = np.load(input_file)
    except Exception as e:
        print(f"读取文件 {input_file} 时出错: {e}")
        return

    # 检查数据维度
    if ground_motions.ndim != 2:
        print(f"错误: 地震动数据维度错误: {ground_motions.shape}, 应为(n, T)")
        return

    n_motions, n_steps = ground_motions.shape
    print(f"地震动数量: {n_motions}, 时间步数: {n_steps}")

    # 初始化数据结构
    n_calculated = 0
    pier_displacements_all = None
    bearing_displacements_all = None
    bearing_forces_all = None

    # 检查是否存在已有结果
    if os.path.exists(output_file):
        try:
            existing_data = np.load(output_file)
            pier_displacements_all = existing_data.get('pier_displacements', None)
            bearing_displacements_all = existing_data.get('bearing_displacements', None)
            bearing_forces_all = existing_data.get('bearing_forces', None)

            # 检查已计算几条地震动
            if bearing_displacements_all is not None and bearing_displacements_all.size > 0:
                n_calculated = np.count_nonzero(bearing_displacements_all.sum((0, 2)))
            else:
                n_calculated = 0
            print(f"已计算地震动数量: {n_calculated}")

            if n_calculated == n_motions:
                print("所有地震动均已计算, 跳过分析")
                return
            else:
                print(f"继续计算剩余地震动: {n_calculated+1} 到 {n_motions}")
        except Exception as e:
            print(f"读取已有结果文件时出错: {e}")
            n_calculated = 0

    # 对每个地震动执行分析
    for i in range(n_calculated, n_motions):
        print(f"\n处理地震动 {i+1}/{n_motions}")

        # 获取当前地震动
        ground_motion = ground_motions[i, :]

        try:
            # 执行分析
            pier_data, bearing_data, _ = run_analysis(
                params, ground_motion, pga=pga, dt=dt
            )

            # 处理分析结果
            pier_disps, bearing_disps, bearing_forces = process_analysis_results(
                pier_data, bearing_data, n_steps
            )

            # 初始化数组（如果是第一次）
            if pier_displacements_all is None or bearing_displacements_all is None or bearing_forces_all is None:
                n_piers = pier_disps.shape[0]
                n_bearings = bearing_disps.shape[0]

                # 处理单跨结构（无桥墩）的情况
                if n_piers == 0:
                    print("检测到单跨结构（无桥墩），仅记录支座响应")
                    pier_displacements_all = np.zeros((1, n_motions, n_steps))  # 保持维度一致性，使用占位符
                else:
                    pier_displacements_all = np.zeros((n_piers, n_motions, n_steps))

                bearing_displacements_all = np.zeros((n_bearings, n_motions, n_steps))
                bearing_forces_all = np.zeros((n_bearings, n_motions, n_steps))

            # 存储当前地震动的结果
            if pier_disps.shape[0] > 0:  # 有桥墩数据时才存储
                pier_displacements_all[:pier_disps.shape[0], i, :] = pier_disps
            bearing_displacements_all[:, i, :] = bearing_disps
            bearing_forces_all[:, i, :] = bearing_forces

            # 保存中间结果
            save_results(
                output_file,
                pier_displacements_all,
                bearing_displacements_all,
                bearing_forces_all,
                pier_data,
                bearing_data
            )

            print(f"地震动 {i+1} 处理完成")

        except Exception as e:
            print(f"处理地震动 {i+1} 时出错: {e}")
            import traceback
            traceback.print_exc()

    print(f"\n分析完成! 结果已保存至: {output_file}")
    print_analysis_summary(output_file)


def save_results(output_file: str, pier_displacements: np.ndarray,
                bearing_displacements: np.ndarray, bearing_forces: np.ndarray,
                pier_data: Dict, bearing_data: Dict):
    """
    保存分析结果为npz格式

    参数:
        output_file: 输出文件路径
        pier_displacements: 桥墩位移数组
        bearing_displacements: 支座位移数组
        bearing_forces: 支座剪力数组
        pier_data: 桥墩数据字典（用于保存元数据）
        bearing_data: 支座数据字典（用于保存元数据）
    """
    try:
        # 准备元数据
        pier_keys = list(pier_data.keys()) if pier_data else []
        bearing_indices = list(bearing_data.keys()) if bearing_data else []

        # 添加结构类型标识
        is_single_span = len(pier_keys) == 0

        # 保存为npz格式
        np.savez_compressed(
            output_file,
            pier_displacements=pier_displacements,
            bearing_displacements=bearing_displacements,
            bearing_forces=bearing_forces,
            pier_keys=pier_keys,
            bearing_indices=bearing_indices,
            is_single_span=is_single_span  # 添加结构类型标识
        )
    except Exception as e:
        print(f"保存结果文件时出错: {e}")
        raise


def print_analysis_summary(output_file: str):
    """
    打印分析结果摘要

    参数:
        output_file: 结果文件路径
    """
    try:
        if not os.path.exists(output_file):
            print("结果文件不存在，无法生成摘要")
            return

        data = np.load(output_file)

        print("\n=== 分析结果摘要 ===")

        # 检查是否为单跨结构
        is_single_span = data.get('is_single_span', False)
        if is_single_span:
            print("结构类型: 单跨结构（无桥墩）")
        else:
            print("结构类型: 多跨结构（含桥墩）")

        if 'pier_displacements' in data:
            pier_disps = data['pier_displacements']
            n_piers, n_motions, n_steps = pier_disps.shape

            # 对于单跨结构，实际桥墩数量为0
            actual_n_piers = 0 if is_single_span else n_piers
            print(f"桥墩数量: {actual_n_piers}")
            print(f"地震动数量: {n_motions}")
            print(f"时间步数: {n_steps}")

            # 只有在有桥墩时才计算最大位移
            if not is_single_span and n_piers > 0:
                max_pier_disp = np.max(np.abs(pier_disps))
                print(f"桥墩最大位移: {max_pier_disp:.6f} m")

        if 'bearing_displacements' in data:
            bearing_disps = data['bearing_displacements']
            n_bearings = bearing_disps.shape[0]
            print(f"支座数量: {n_bearings}")

            # 计算最大支座位移
            max_bearing_disp = np.max(np.abs(bearing_disps))
            print(f"支座最大位移: {max_bearing_disp:.6f} m")

        if 'bearing_forces' in data:
            bearing_forces = data['bearing_forces']

            # 计算最大支座剪力
            max_bearing_force = np.max(np.abs(bearing_forces))
            print(f"支座最大剪力: {max_bearing_force:.2f} N")

        print("=" * 25)

    except Exception as e:
        print(f"生成分析摘要时出错: {e}")


def validate_input_parameters(input_file: str, config_file: str, dt: float, pga: float) -> bool:
    """
    验证输入参数的有效性

    参数:
        input_file: 地震动文件路径
        config_file: 配置文件路径
        dt: 时间步长
        pga: 峰值地面加速度

    返回:
        bool: 参数是否有效
    """
    # 检查文件存在性
    if not os.path.exists(input_file):
        print(f"错误: 地震动文件 {input_file} 不存在")
        return False

    if not os.path.exists(config_file):
        print(f"错误: 配置文件 {config_file} 不存在")
        return False

    # 检查参数范围
    if dt <= 0 or dt > 0.1:
        print(f"警告: 时间步长 {dt} 可能不合理，建议范围: (0, 0.1]")

    if pga <= 0 or pga > 2.0:
        print(f"警告: 峰值地面加速度 {pga} 可能不合理，建议范围: (0, 2.0]")

    return True


if __name__ == "__main__":
    """
    脚本入口点

    执行地震动时程分析，包括：
    1. 自动选择代表性桥墩和支座
    2. 记录桥墩位移、支座位移和支座剪力
    3. 将结果保存为npz格式文件
    """
    try:
        main()
    except KeyboardInterrupt:
        print("\n用户中断分析")
    except Exception as e:
        print(f"\n分析过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
